# 测试环境通用配置

# Java环境配置
JAVA_HOME="/jdk1.8.0_231"

# Git默认配置
GIT_DEFAULT_BRANCH="develop"

# 代码目录配置
CODE_BASE_DIR="/root/code"

# 是否需要登录Docker仓库
NEED_LOGIN_DOCKER="false"

# Docker登录配置
DOCKER_REGISTRY="registry.cn-hangzhou.aliyuncs.com"
DOCKER_USERNAME="kimi.li@1832066708392168"
DOCKER_PASSWORD="lgq086539"
DEFAULT_IMAGE_VERSION="0.0.20"

# 镜像库地址配置
IMAGE_ADDRESS="************:5000"

# 远程服务配置，浙一生产环境，不建议直接更新服务，每次需要打包更新镜像固定版本
DEFAULT_REMOTE_HOST="root@*************"
RESTART_WAIT_TIME=15  # 等待容器重启的时间（秒）

# 默认功能开关
DEFAULT_UPDATE_IMAGE="true"
DEFAULT_UPDATE_REMOTE_SERVICE="false"