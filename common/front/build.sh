#!/bin/bash
set -euo pipefail  # 严格模式：错误退出、未定义变量报错、管道错误检测

# ========== 前端构建脚本 ==========
# 该脚本负责更新代码并构建前端项目

# 检查必要的环境变量
check_required_vars() {
    local required_vars=("BRANCH_DIR" "GIT_REPO" "GIT_BRANCH" "SUB_PROJECT" "BUILD_COMMAND" "DIST_DIR" "INSTALL_DEPENDENCIES_COMMAND")
    for var in "${required_vars[@]}"; do
        if [ -z "${!var:-}" ]; then
            echo "错误：缺少必要的环境变量 $var"
            exit 1
        fi
    done
    echo "✓ 环境变量检查通过"
}

# 更新代码
update_code() {
    echo "→ 开始处理代码仓库..."
    cd "$BRANCH_DIR" || { echo "错误：目录 $BRANCH_DIR 不存在"; exit 1; }

    # 如果存在任何旧目录，先清理
    if [ ! -d "$PROJECT" ]; then
        echo "下载代码库到  $BRANCH_DIR/$PROJECT..."
        git clone -b "$GIT_BRANCH" "$GIT_REPO"
        cd "$PROJECT"
    else
        cd "$PROJECT"
        echo "→ 更新代码到 $BRANCH_DIR/$PROJECT..."
        git pull
    fi

    # 检查克隆后的目录结构
    echo "→ 代码目录结构:"
    ls -la "$BRANCH_DIR/$PROJECT"

    echo "✓ 代码更新完成"
}

# 安装Node.js依赖并构建项目
build_project() {
    echo "→ 开始构建 $SUB_PROJECT 项目..."
    cd "$BRANCH_DIR/$PROJECT" || { echo "错误：代码目录不存在"; exit 1; }

    # 检查是否需要设置Node.js版本
# 检查是否需要设置Node.js版本
if [ -n "${NODE_VERSION:-}" ]; then
    echo "→ 设置Node.js版本: $NODE_VERSION"

    # 加载nvm（确保在脚本中可用）
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

    if command -v nvm &> /dev/null; then
        echo "→ 当前Node.js版本: $(node -v 2>/dev/null || echo '未安装')"
        nvm use "$NODE_VERSION" || {
            echo "→ 版本 $NODE_VERSION 不存在，正在安装..."
            nvm install "$NODE_VERSION"
            nvm use "$NODE_VERSION"
        }
        echo "→ 切换后Node.js版本: $(node -v)"
    else
        echo "警告: nvm不可用，使用系统默认Node.js"
    fi
fi

    # 显示Node.js和npm版本
    echo "→ Node.js版本: $(node -v 2>/dev/null || echo '未安装')"
    echo "→ npm版本: $(npm -v 2>/dev/null || echo '未安装')"
    echo "→ yarn版本: $(yarn --version 2>/dev/null || echo '未安装')"

    # 安装依赖
    echo "→ 安装依赖..."
    #yarn
    eval "$INSTALL_DEPENDENCIES_COMMAND"

    # 清理历史构建
    echo "→ 清理历史构建目录：$BRANCH_DIR/$PROJECT/$DIST_DIR"
    rm -rf "$DIST_DIR"/*
    # 执行构建命令
    echo "→ 执行构建命令: $BUILD_COMMAND"
    eval "$BUILD_COMMAND"

    # 检查构建结果
    if [ ! -d "$DIST_DIR" ]; then
        echo "错误：构建后的目录不存在: $DIST_DIR"
        exit 1
    fi

    echo "✓ $SUB_PROJECT 项目构建完成，构建目录: $BRANCH_DIR/$PROJECT/$DIST_DIR"
}

# ========== 主流程 ==========
main() {
    echo "====== 开始执行前端构建流程 [$(date +'%Y-%m-%d %H:%M:%S')] ======"
    check_required_vars
    update_code
    build_project
    echo "====== 前端构建流程执行完毕 [耗时: $SECONDS 秒] ======"
}

# 执行主函数
main