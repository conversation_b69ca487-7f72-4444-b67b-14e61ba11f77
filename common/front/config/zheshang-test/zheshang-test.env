# 测试环境通用配置

# Git默认配置
GIT_DEFAULT_BRANCH="dev"

# 代码目录配置
CODE_BASE_DIR="/root/code"

# 是否需要登录Docker仓库
NEED_LOGIN_DOCKER="false"

# Docker登录配置
DOCKER_REGISTRY="registry.cn-hangzhou.aliyuncs.com"
DOCKER_USERNAME="kimi.li@1832066708392168"
DOCKER_PASSWORD="lgq086539"
DEFAULT_IMAGE_VERSION="0.0.20-zheshang"

# 镜像库地址配置
IMAGE_ADDRESS="************:5000"

# 远程服务配置
DEFAULT_REMOTE_HOST="root@*************"

# 默认功能开关
DEFAULT_UPDATE_IMAGE="false"
DEFAULT_UPDATE_REMOTE_SERVICE="false"