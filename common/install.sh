
### 新项目安装过程
### 目前暂未实现自动化脚本，有些需要人工介入操作

# 新建相关目录
cd /
# rancher数据存放位置
mkdir -p /data/rancher/rancher-data
# mysql 数据存放位置
mkdir -p /data/mysql/data
# redis 数据存放位置
mkdir -p /data/redis/data
mkdir -p /data/redis/logs
# rabbitmq 数据存放位置
mkdir -p /data/rabbitmq/data
# mongo 数据存放位置
mkdir -p /data/mongo/db
# taos 数据存放位置
mkdir -p /data/taos/data
# fastdfs 数据存放位置
mkdir -p /data/fdfs/tracker
mkdir -p /data/fdfs/storage
# kafka 数据存放位置
mkdir -p /data/kafka/data
mkdir -p /data/kafka/logs
# nacos 数据存放位置
mkdir -p /data/nacos/logs
# xxl-job 日志
mkdir -p /data/job-admin/xxl-job
# inxvision 应用日志目录
mkdir -p /data/inxvision/logs



# 安装rancher
# 登录阿里云docker 镜像库
docker login --username=kimi.li@1832066708392168 --password=lgq086539 registry.cn-hangzhou.aliyuncs.com

# 设置内核参数
# 在某些设备上安装，可能会启动rancher失败，日志类似以下，需要手动修改下内核参数：
# F0526 18:19:05.188312 31 server.go:495] open /proc/sys/net/netfilter/nf_conntrack_max: permission denied
# 2025/05/26 18:19:05 [FATAL] k3s exited with: exit status 1
# 如果以下修改不生效，还报这个错，按这个修改：
# sudo vi /etc/sysctl.conf
# 末尾一行增加以下内容
# net.netfilter.nf_conntrack_max = 262144
# sudo sysctl -p
sudo sysctl -w net.netfilter.nf_conntrack_max=131072

# 安装rancher，单机安装rancher端口必须指定8443，不然会冲突，多服务器节点安装可以使用443端口
sudo docker run -d --privileged --restart=unless-stopped -p 8080:80 -p 8443:443 -v /data/rancher/rancher-data:/var/lib/rancher/ -v /etc/localtime:/etc/localtime registry.cn-hangzhou.aliyuncs.com/inxvision/weef-base-rancher:2.5.9

# 如果报以下错误
# Failed to ensure iptables rules: Error checking rule existence: failed to check rule existence: running [/var/lib/rancher/k3s/data/ab1b521f47b3cbc273e0c667521084cff1ea3540d55b425ac7ed30c27daf92f8/bin/aux/iptables -t nat -C POSTROUTING -s *********/16 -d *********/16 -j RETURN --wait]: exit status 3: iptables v1.8.5 (legacy): can't initialize iptables table `nat': Table does not exist (do you need to insmod?)
# Perhaps iptables or your kernel needs to be upgraded.
# 需要开启以下命令
# sudo modprobe ip_tables
# sudo modprobe iptable_nat
# sudo modprobe iptable_filter
# 然后写入以下配置，保证每次重次也这样：
# sudo vi /etc/modules-load.d/rancher-k3s.conf
# 写入以下内容
# ip_tables
# iptable_nat
# iptable_filter


# 初始化rancher 节点，如果出现拉取镜像超时，可能要把下面一步（修改镜像源）提前做
# 1. 登录rancher，设置初始密码(项目上默认为inxvision)
# 2. 添加节点 add cluster——> Create a new Kubernetes cluster——> Existing nodes, 输入节点名，下一步
# 3. 选择节点角色，如果是单机部署，全部都选上
# 3. 复制命令到节点执行
# 4. 等待节点加入成功



# 安装本地镜像库
# 修改docker 镜像源地址
vi /etc/docker/daemon.json
# 添加以下内容，加入私库地址，其中"registry-mirrors为公网能拉取公有镜像的地址，这个需要即时查询哪些可用
# "insecure-registries"为私有镜像仓库地址，这个需要根据实际情况修改为本机IP，方便本地能拉取镜像访问，如果是其他节点服务器，则需要修改本地IP
# {
#   "registry-mirrors":["https://docker.1ms.run","https://docker.xuanyuan.me"],
#   "insecure-registries":["************:8080","************:5000","*********/8"]
# }
# 重启docker，生效上面镜像源配置
sudo systemctl daemon-reload
sudo systemctl restart docker

# 镜像文件数据文件夹
mkdir -p /data/docker/registry-data
mkdir -p /data/docker/registry-ui
# 安装docker registry，采用docker-compose安装
cd /data/docker
vi docker-compose.yml
# 添加以下内容， 注意，如果本地安装，UI界面8080端口可能会和其他服务冲突，修改为其他端口
# 安装完成后，可以通过http://镜像库服务器IP:5001访问UI界面，查看镜像资源
version: '3.0'
services:
  registry:
    image: registry
    ports:
      - "5000:5000"
    volumes:
      - /data/docker/registry-data:/var/lib/registry
    restart: always
  ui:
    image: joxit/docker-registry-ui:static
    ports:
      - 5001:80
    environment:
      - REGISTRY_TITLE=私有镜像仓库
      - REGISTRY_URL=http://registry:5000
      - CATALOG_ELEMENTS_LIMIT=1000
    depends_on:
      - registry
    restart: always
# 启动docker registry
docker-compose up -d


# 修改docker 镜像源地址
# 1. 修改docker 配置文件
# sudo vi /etc/docker/daemon.json
# 2. 添加以下内容，加入私库地址
# {
#   "insecure-registries": ["************:5000", "127.0.0.1:5000"]
# }
# 3. 重启docker
# sudo systemctl daemon-reload
# sudo systemctl restart docker


# 在Rancher里面导入deployment yaml文件，安装服务


# TD 查看节点名， 登录容器
cat /opt/data/taos/taosdata/dnode/dnodeEps.json
# 不变更镜像，名字一般定义为 weef-base-taos-66dd696c4d-fbsbj
# 增加TD用户和数据库, 注意和deployment yaml文件配置保持一致
taos -u root -p
alter user root pass 'inxvision';
create database inxvision;

# Rabbitmq配置 注意和deployment yaml文件配置保持一致
# 登录管理界面  http://安装服务器IP:15672/, 账号密码和demployment yaml文件配置保持一致(inxvision)
# 切换到 admin——> Virtual Hosts
# 增加 virtual host: inxvision_prd，并确保inxvision用户（与yaml文件配置一致）有访问权限







